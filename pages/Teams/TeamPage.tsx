import React from 'react';
import { View } from 'react-native';
import { type Team } from '@/types/teams';
import { TabItem } from '@/types/tab';
import CollapsibleTabView from '@/components/CollapsibleTabView';
import TeamOverview from '@/pages/Teams/TeamOverview';
import TeamSquad from '@/pages/Teams/TeamSquad';
import TeamMatches from '@/pages/Teams/TeamMatches';
import TeamStats from '@/pages/Teams/TeamStats';
import { teamTabState } from '@/atoms/teamTabs';
import { useRecoilState } from 'recoil';

interface TeamPageProps {
  team: Team;
  tournamentId: string;
  onRefetch?: () => void;
}

interface TeamPageData {
  team: Team;
  tournamentId: string;
  onRefetch?: () => void;
}

const teamTabs: TabItem<TeamPageData>[] = [
  {
    id: 'overview',
    name: 'Overview',
    render: (data: TeamPageData) => (
      <TeamOverview
        team={data.team}
        tournamentId={data.tournamentId}
        onRefetch={data.onRefetch}
      />
    ),
  },
  {
    id: 'squad',
    name: 'Squad',
    render: (data: TeamPageData) => (
      <TeamSquad team={data.team} tournamentId={data.tournamentId} />
    ),
  },
  {
    id: 'matches',
    name: 'Matches',
    render: (data: TeamPageData) => (
      <TeamMatches team={data.team} tournamentId={data.tournamentId} />
    ),
  },
  {
    id: 'stats',
    name: 'Stats',
    render: (data: TeamPageData) => (
      <TeamStats team={data.team} tournamentId={data.tournamentId} />
    ),
  },
];

const TeamPage: React.FC<TeamPageProps> = ({
  team,
  tournamentId,
  onRefetch,
}) => {
  const teamData: TeamPageData = {
    team,
    tournamentId,
    onRefetch,
  };
  const [tabState, setTabState] = useRecoilState(teamTabState);
  const selectedTab = tabState[team.id] || 'overview';

  const handleTabChange = ({ tabName }: { tabName: string }) => {
    setTabState((prev) => ({
      ...prev,
      [team.id]: tabName,
    }));
  };

  return (
    <CollapsibleTabView
      data={teamData}
      tabs={teamTabs}
      lazy={true}
      initialTabName={selectedTab}
      onTabChange={handleTabChange}
    />
  );
};

export default TeamPage;
