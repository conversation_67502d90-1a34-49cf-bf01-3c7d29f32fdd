import { toast } from '@/toast/toast';
import { updateTeam } from '@/services/teamsService';
import {
  type SocialPlatform,
  SocialPlatformKeyMap,
} from '@/components/k-components/SocialLinkEditor';

export const useTeamSocialLinks = (teamId: string) => {
  const handleSocialLinkSave = async (
    platform: SocialPlatform,
    url: string
  ) => {
    const key = SocialPlatformKeyMap[platform];
    console.log('>>>>', {
      teamId,
      team: { [key]: url },
    });
    const { success, error } = await updateTeam({
      teamId,
      team: { [key]: url },
    });

    if (success) {
      toast.success('Social link updated successfully');
    } else {
      toast.error(error || 'Failed to update social link');
    }

    return { success, error };
  };

  return {
    handleSocialLinkSave,
  };
};
