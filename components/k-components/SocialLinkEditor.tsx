import React, { useState, useEffect, memo, useMemo } from 'react';
import { View, Linking } from 'react-native';
import {
  Actionsheet,
  ActionsheetBackdrop,
  ActionsheetContent,
  ActionsheetDragIndicator,
  ActionsheetDragIndicatorWrapper,
} from '@/components/ui/actionsheet';
import { Text } from '@/components/ui/text';
import { Button, ButtonText } from '@/components/ui/button';
import { CustomInput } from '@/components/ui/customInput';
import { Icon } from '@/components/ui/icon';
import {
  InstagramIcon,
  FacebookIcon,
  GlobeIcon,
  ExternalLinkIcon,
} from '@/components/ui/icon';
import { validateUrl } from '@/utils';
import { ErrorText } from '../ui/errortext';

const validatePlatformUrl = (
  url: string,
  platform: SocialPlatform
): string | null => {
  if (!url || url.trim() === '') return null; // Optional field

  const basicValidation = validateUrl(url);
  if (basicValidation) return basicValidation;

  try {
    const urlObj = new URL(url);
    const hostname = urlObj.hostname.toLowerCase();

    switch (platform) {
      case 'instagram':
        if (
          !hostname.includes('instagram.com') &&
          !hostname.includes('instagr.am')
        ) {
          return 'Please enter a valid Instagram URL (instagram.com)';
        }
        break;
      case 'facebook':
        if (
          !hostname.includes('facebook.com') &&
          !hostname.includes('fb.com')
        ) {
          return 'Please enter a valid Facebook URL (facebook.com)';
        }
        break;
      case 'website':
        break;
      default:
        return 'Invalid platform';
    }

    return null;
  } catch {
    return 'Please enter a valid URL';
  }
};

export type SocialPlatform = 'instagram' | 'facebook' | 'website';

export const SocialPlatformKeyMap = {
  instagram: 'instagram_url',
  facebook: 'facebook_url',
  website: 'website',
};

interface SocialLinkEditorProps {
  isOpen: boolean;
  onClose: () => void;
  platform: SocialPlatform;
  initialValue?: string;
  onSave: (url: string) => void;
}

const platformConfig = {
  instagram: {
    icon: InstagramIcon,
    title: 'Official Instagram Page',
    placeholder: 'https://instagram.com/yourteam',
  },
  facebook: {
    icon: FacebookIcon,
    title: 'Official Facebook Page',
    placeholder: 'https://facebook.com/yourteam',
  },
  website: {
    icon: GlobeIcon,
    title: 'Official Team Website',
    placeholder: 'https://yourteam.com',
  },
};

const SocialLinkEditor: React.FC<SocialLinkEditorProps> = ({
  isOpen,
  onClose,
  platform,
  initialValue = '',
  onSave,
}) => {
  const [url, setUrl] = useState(initialValue);
  const [error, setError] = useState<string | null>(null);

  const config = platformConfig[platform];

  useEffect(() => {
    setUrl(initialValue);
    setError(null);
  }, [initialValue]);

  const handleCancel = () => {
    setUrl(initialValue);
    setError(null);
    onClose();
  };

  const handleSave = () => {
    const validationError = validatePlatformUrl(url, platform);
    if (validationError) {
      setError(validationError);
      return;
    }
    onSave(url.trim());
    onClose();
  };

  const handleRedirect = async () => {
    if (!url.trim()) return;
    const validationError = validatePlatformUrl(url, platform);
    if (!validationError) {
      try {
        const canOpen = await Linking.canOpenURL(url.trim());
        if (canOpen) {
          await Linking.openURL(url.trim());
        }
      } catch {}
    }
  };

  const isValidUrl = useMemo(() => {
    return !validatePlatformUrl(url, platform) && url.trim() !== '';
  }, [url, platform]);

  return (
    <Actionsheet isOpen={isOpen} onClose={handleCancel}>
      <ActionsheetBackdrop />
      <ActionsheetContent className="px-4">
        <ActionsheetDragIndicatorWrapper>
          <ActionsheetDragIndicator />
        </ActionsheetDragIndicatorWrapper>
        <View className="w-full items-center mb-6 pt-4 flex">
          <Icon
            size="xl"
            as={config.icon}
            className="text-typography-700 mb-3"
          />
          <Text className="text-xl font-urbanistBold text-typography-700 text-center">
            {config.title}
          </Text>
        </View>

        <View className="w-full mb-6">
          <View className="flex-row items-center gap-2">
            <View className="flex-1">
              <CustomInput
                value={url}
                onChangeText={(text) => {
                  setUrl(text);
                  setError(null);
                }}
                placeholder={config.placeholder}
                autoCorrect={false}
                type="text"
              />
            </View>
            <Button
              size="sm"
              action="positive"
              variant="solid"
              onPress={handleRedirect}
              isDisabled={!isValidUrl}
              className="w-12 h-12 rounded-lg"
            >
              <Icon as={ExternalLinkIcon} size="sm" className="text-white" />
            </Button>
          </View>
          <ErrorText message={error} />
        </View>

        <View className="flex-row gap-3 w-full">
          <Button
            variant="outline"
            action="secondary"
            onPress={handleCancel}
            className="flex-1"
          >
            <ButtonText className="font-urbanistSemiBold">Cancel</ButtonText>
          </Button>
          <Button
            variant="solid"
            action="primary"
            onPress={handleSave}
            className="flex-1 bg-primary-0"
          >
            <ButtonText className="font-urbanistSemiBold">Save</ButtonText>
          </Button>
        </View>
      </ActionsheetContent>
    </Actionsheet>
  );
};

export default memo(SocialLinkEditor);
