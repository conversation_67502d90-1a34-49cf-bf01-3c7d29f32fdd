import { useFocusEffect, useLocalSearchPara<PERSON>, useRouter } from 'expo-router';
import { useCallback, useState } from 'react';
import { NavLayout } from '@/components/NavLayout';
import { toast } from '@/toast/toast';
import FullscreenLoader from '@/components/k-components/FullscreenLoader';
import { fetchTeamById } from '@/services/teamsService';
import TeamPage from '@/pages/Teams/TeamPage';
import { type Team } from '@/types/teams';
import { CustomMenu, type MenuOption } from '@/components/CustomMenu';
import { EditIcon, TrashIcon } from '@/components/ui/icon';
import TeamDeleteConfirmationModal from '../../../../pages/Teams/components/TeamDeleteConfirmationModal';
import SCREENS from '@/constants/Screens';

export default function TeamViewScreen() {
  const router = useRouter();
  const { 'team-id': teamId, 'tournament-id': tournamentId } =
    useLocalSearchParams();

  const [team, setTeam] = useState<Team | null>(null);
  const [loading, setLoading] = useState(true);
  const [showDeleteModal, setShowDeleteModal] = useState(false);

  const loadTeam = useCallback(async () => {
    if (!teamId) return;

    setLoading(true);
    try {
      const {
        success,
        team: fetchedTeam,
        error,
      } = await fetchTeamById(teamId as string);

      if (!success) {
        toast.error(error || 'Failed to load team details');
        router.back();
        return;
      }

      setTeam(fetchedTeam);
    } catch (error) {
      toast.error('Something went wrong');
      router.back();
    } finally {
      setLoading(false);
    }
  }, [teamId, router]);

  useFocusEffect(
    useCallback(() => {
      loadTeam();
    }, [loadTeam])
  );

  const menuOptions: MenuOption[] = [
    {
      key: 'edit-team',
      label: 'Edit Team',
      icon: EditIcon,
      onPress: () => {
        router.push({
          pathname: SCREENS.TEAM_EDIT,
          params: {
            'team-id': teamId,
            'tournament-id': tournamentId,
          },
        });
      },
    },
    {
      key: 'delete-team',
      label: 'Delete Team',
      icon: TrashIcon,
      onPress: () => setShowDeleteModal(true),
      optionClassName: 'text-red-500',
      separatorBefore: true,
    },
  ];

  return (
    <>
      <NavLayout
        title={team?.name || 'Team Details'}
        isFullscreen
        right={
          team && (
            <CustomMenu
              options={menuOptions}
              placement="right top"
              offset={-20}
            />
          )
        }
      >
        {loading && <FullscreenLoader />}
        {!loading && team && (
          <TeamPage team={team} tournamentId={tournamentId as string} />
        )}
      </NavLayout>

      <TeamDeleteConfirmationModal
        isOpen={showDeleteModal}
        onClose={() => setShowDeleteModal(false)}
        team={team}
      />
    </>
  );
}
