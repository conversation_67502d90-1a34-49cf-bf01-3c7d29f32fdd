import { Icon } from '@/components/ui/icon';
import { Text } from '@/components/ui/text';
import { useClientOnlyValue } from '@/components/useClientOnlyValue';
import { KanbanIcon, TrophyIcon, UserIcon } from 'lucide-react-native';
import { useRef } from 'react';
import { TouchableOpacity, Vibration, Platform, Animated } from 'react-native';
import * as Haptics from 'expo-haptics';

export const TAB_CONFIG = {
  tournaments: {
    title: 'Tournaments',
    icon: TrophyIcon,
  },
  manage: {
    title: 'Manage',
    icon: KanbanIcon,
  },
  account: {
    title: 'Account',
    icon: UserIcon,
  },
} as const;

export function triggerHapticFeedback() {
  if (Platform.OS === 'ios') {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
  } else {
    Vibration.vibrate(10);
  }
}

export function getTabOptions(routeName: string) {
  const config = TAB_CONFIG[routeName as keyof typeof TAB_CONFIG];
  if (!config) return {};

  const { icon, title } = config;

  return {
    headerShown: useClientOnlyValue(false, false),
    tabBarStyle: {
      position: 'absolute',
      backgroundColor: '#ffffff',
      borderTopLeftRadius: 25,
      borderTopRightRadius: 25,
      overflow: 'hidden',
      height: 65,
      paddingBottom: 8,
      paddingTop: 8,
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.04,
      shadowRadius: 6,
      elevation: 8,
      borderWidth: 1,
      borderBottomWidth: 0,
    },

    tabBarButton: (props: any) => {
      const scaleAnim = useRef(new Animated.Value(1)).current;

      const handlePressIn = () => {
        triggerHapticFeedback();
        Animated.spring(scaleAnim, {
          toValue: 0.9,
          friction: 5,
          useNativeDriver: true,
        }).start();
      };

      const handlePressOut = () => {
        Animated.spring(scaleAnim, {
          toValue: 1,
          friction: 5,
          useNativeDriver: true,
        }).start();
      };

      return (
        <Animated.View
          style={{
            flex: 1,
            transform: [{ scale: scaleAnim }],
          }}
        >
          <TouchableOpacity
            {...props}
            activeOpacity={1}
            onPressIn={handlePressIn}
            onPressOut={handlePressOut}
            style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}
          >
            {props.children}
          </TouchableOpacity>
        </Animated.View>
      );
    },

    tabBarLabel: ({ focused }: { focused: boolean }) => (
      <Text
        size="sm"
        className={`text-sm font-urbanistSemiBold ${
          focused ? 'text-typography-900' : 'text-typography-400 '
        }`}
      >
        {title}
      </Text>
    ),
    tabBarIcon: ({ focused }: { focused: boolean }) => (
      <Icon
        as={icon}
        //@ts-ignore
        size="2xl"
        className={focused ? 'text-typography-900' : 'text-typography-400'}
      />
    ),
  };
}

export function validateEmail(email?: string | null) {
  if (!email || email.trim() === '') return 'Email is required';

  const re = /^\S+@\S+\.\S+$/;
  return re.test(email) ? null : 'Invalid email';
}

export const validatePassword = (password: string) =>
  !password || password.length < 6
    ? 'Password must be at least 6 characters'
    : null;

export function validateUrl(url?: string | null): string | null {
  if (!url || url.trim() === '') return null; // Optional field

  try {
    const urlObj = new URL(url);
    return urlObj.protocol === 'http:' || urlObj.protocol === 'https:'
      ? null
      : 'URL must start with http:// or https://';
  } catch {
    return 'Please enter a valid URL';
  }
}

export function cn(...classes: (string | false | null | undefined)[]): string {
  return classes.filter(Boolean).join(' ');
}

export const generateRandomId = () =>
  'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {
    const r = (Math.random() * 16) | 0;
    const v = c === 'x' ? r : (r & 0x3) | 0x8;
    return v.toString(16);
  });

export function capitalizeFirst(text?: string): string | undefined {
  if (!text) return undefined;
  return text.charAt(0).toUpperCase() + text.slice(1);
}

export const getInitials = (text: string): string => {
  if (!text) return '?';
  return text
    .split(' ')
    .filter(Boolean)
    .slice(0, 3)
    .map((word) => word[0]?.toUpperCase())
    .join('');
};

export const getReadableTextColor = (hexColor: string): 'white' | 'black' => {
  const hex = hexColor.replace('#', '');
  const r = parseInt(hex.substring(0, 2), 16);
  const g = parseInt(hex.substring(2, 4), 16);
  const b = parseInt(hex.substring(4, 6), 16);

  // Calculate luminance
  const luminance = (0.299 * r + 0.587 * g + 0.114 * b) / 255;

  return luminance > 0.6 ? 'black' : 'white';
};
